<?php
require_once 'config/config.php';

// Check if user is logged in and is admin (optional)
if (!isset($_SESSION['user_id'])) {
    $_SESSION['intended_url'] = $_SERVER['REQUEST_URI'];
    redirect(SITE_URL . '/login.php');
}

$page_title = 'Setup Profile Features';
$page_description = 'Setup and verify all profile-related database tables and features.';

$setup_results = [];
$success_count = 0;
$total_count = 0;

try {
    $pdo = getDBConnection();
    
    // 1. Create user_addresses table if it doesn't exist
    $total_count++;
    try {
        $sql = "CREATE TABLE IF NOT EXISTS user_addresses (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            type ENUM('home', 'work', 'other') DEFAULT 'home',
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHA<PERSON>(100) NOT NULL,
            company VARCHAR(100),
            address_line_1 VARCHAR(255) NOT NULL,
            address_line_2 VARCHAR(255),
            city VARCHAR(100) NOT NULL,
            state VARCHAR(100) NOT NULL,
            postal_code VARCHAR(20) NOT NULL,
            country VARCHAR(100) DEFAULT 'Indonesia',
            phone VARCHAR(20),
            is_default BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user (user_id),
            INDEX idx_default (is_default)
        )";
        
        $pdo->exec($sql);
        $setup_results['user_addresses_table'] = [
            'title' => 'User Addresses Table',
            'status' => 'SUCCESS',
            'message' => 'Table created/verified successfully'
        ];
        $success_count++;
    } catch (Exception $e) {
        $setup_results['user_addresses_table'] = [
            'title' => 'User Addresses Table',
            'status' => 'ERROR',
            'message' => 'Error: ' . $e->getMessage()
        ];
    }
    
    // 2. Verify orders table exists
    $total_count++;
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'orders'");
        if ($stmt->rowCount() > 0) {
            $setup_results['orders_table'] = [
                'title' => 'Orders Table',
                'status' => 'SUCCESS',
                'message' => 'Table exists and is accessible'
            ];
            $success_count++;
        } else {
            $setup_results['orders_table'] = [
                'title' => 'Orders Table',
                'status' => 'WARNING',
                'message' => 'Orders table does not exist - some features may not work'
            ];
        }
    } catch (Exception $e) {
        $setup_results['orders_table'] = [
            'title' => 'Orders Table',
            'status' => 'ERROR',
            'message' => 'Error checking orders table: ' . $e->getMessage()
        ];
    }
    
    // 3. Verify wishlist table exists
    $total_count++;
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'wishlist'");
        if ($stmt->rowCount() > 0) {
            $setup_results['wishlist_table'] = [
                'title' => 'Wishlist Table',
                'status' => 'SUCCESS',
                'message' => 'Table exists and is accessible'
            ];
            $success_count++;
        } else {
            $setup_results['wishlist_table'] = [
                'title' => 'Wishlist Table',
                'status' => 'WARNING',
                'message' => 'Wishlist table does not exist - some features may not work'
            ];
        }
    } catch (Exception $e) {
        $setup_results['wishlist_table'] = [
            'title' => 'Wishlist Table',
            'status' => 'ERROR',
            'message' => 'Error checking wishlist table: ' . $e->getMessage()
        ];
    }
    
    // 4. Check if user profile has required fields
    $total_count++;
    try {
        $stmt = $pdo->query("DESCRIBE users");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $required_fields = ['first_name', 'last_name', 'email', 'phone'];
        $missing_fields = array_diff($required_fields, $columns);
        
        if (empty($missing_fields)) {
            $setup_results['user_profile_fields'] = [
                'title' => 'User Profile Fields',
                'status' => 'SUCCESS',
                'message' => 'All required profile fields exist'
            ];
            $success_count++;
        } else {
            $setup_results['user_profile_fields'] = [
                'title' => 'User Profile Fields',
                'status' => 'WARNING',
                'message' => 'Missing fields: ' . implode(', ', $missing_fields)
            ];
        }
    } catch (Exception $e) {
        $setup_results['user_profile_fields'] = [
            'title' => 'User Profile Fields',
            'status' => 'ERROR',
            'message' => 'Error checking user table: ' . $e->getMessage()
        ];
    }
    
    // 5. Create sample address for current user (if none exists)
    $total_count++;
    try {
        $user_id = $_SESSION['user_id'];
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM user_addresses WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $address_count = $stmt->fetch()['count'];
        
        if ($address_count == 0) {
            // Get user info for sample address
            $stmt = $pdo->prepare("SELECT first_name, last_name FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            $user = $stmt->fetch();
            
            if ($user) {
                $stmt = $pdo->prepare("
                    INSERT INTO user_addresses (user_id, type, first_name, last_name, address_line_1, city, state, postal_code, country, is_default)
                    VALUES (?, 'home', ?, ?, 'Jl. Contoh No. 123', 'Jakarta', 'DKI Jakarta', '12345', 'Indonesia', 1)
                ");
                $stmt->execute([$user_id, $user['first_name'], $user['last_name']]);
                
                $setup_results['sample_address'] = [
                    'title' => 'Sample Address',
                    'status' => 'SUCCESS',
                    'message' => 'Sample address created for testing'
                ];
                $success_count++;
            } else {
                $setup_results['sample_address'] = [
                    'title' => 'Sample Address',
                    'status' => 'WARNING',
                    'message' => 'Could not create sample address - user not found'
                ];
            }
        } else {
            $setup_results['sample_address'] = [
                'title' => 'Sample Address',
                'status' => 'SUCCESS',
                'message' => "User already has {$address_count} address(es)"
            ];
            $success_count++;
        }
    } catch (Exception $e) {
        $setup_results['sample_address'] = [
            'title' => 'Sample Address',
            'status' => 'ERROR',
            'message' => 'Error creating sample address: ' . $e->getMessage()
        ];
    }
    
} catch (Exception $e) {
    $setup_results['database_connection'] = [
        'title' => 'Database Connection',
        'status' => 'ERROR',
        'message' => 'Failed to connect to database: ' . $e->getMessage()
    ];
}

include 'includes/header.php';
?>

<div class="container-lg my-5">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h2 mb-1 text-primary">
                        <i class="fas fa-cogs me-2"></i>Profile Features Setup
                    </h1>
                    <p class="text-muted">Setup and verify all profile-related database tables and features</p>
                </div>
                <div>
                    <a href="test-profile-features.php" class="btn btn-outline-success">
                        <i class="fas fa-check-circle me-2"></i>Test Features
                    </a>
                </div>
            </div>

            <!-- Setup Results -->
            <div class="card mb-4 border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-list-check me-2"></i>Setup Results
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($setup_results as $key => $result): ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card h-100 border-<?php 
                                    echo $result['status'] === 'SUCCESS' ? 'success' : 
                                         ($result['status'] === 'WARNING' ? 'warning' : 'danger'); 
                                ?>">
                                    <div class="card-body text-center">
                                        <div class="mb-2">
                                            <i class="fas fa-<?php 
                                                echo $result['status'] === 'SUCCESS' ? 'check-circle text-success' : 
                                                     ($result['status'] === 'WARNING' ? 'exclamation-triangle text-warning' : 'times-circle text-danger'); 
                                            ?> fa-2x"></i>
                                        </div>
                                        <h6 class="card-title"><?php echo $result['title']; ?></h6>
                                        <span class="badge bg-<?php 
                                            echo $result['status'] === 'SUCCESS' ? 'success' : 
                                                 ($result['status'] === 'WARNING' ? 'warning' : 'danger'); 
                                        ?> mb-2">
                                            <?php echo $result['status']; ?>
                                        </span>
                                        <p class="small text-muted mb-0"><?php echo htmlspecialchars($result['message']); ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Summary -->
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <?php
                    $success_rate = $total_count > 0 ? round(($success_count / $total_count) * 100, 1) : 0;
                    ?>
                    
                    <div class="mb-3">
                        <i class="fas fa-chart-pie fa-3x text-<?php echo $success_rate >= 80 ? 'success' : ($success_rate >= 60 ? 'warning' : 'danger'); ?>"></i>
                    </div>
                    
                    <h4 class="mb-3">Setup Summary</h4>
                    
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="progress mb-3" style="height: 20px;">
                                <div class="progress-bar bg-<?php echo $success_rate >= 80 ? 'success' : ($success_rate >= 60 ? 'warning' : 'danger'); ?>" 
                                     style="width: <?php echo $success_rate; ?>%">
                                    <?php echo $success_rate; ?>%
                                </div>
                            </div>
                            
                            <p class="mb-4">
                                <strong><?php echo $success_count; ?></strong> out of <strong><?php echo $total_count; ?></strong> setup tasks completed successfully
                            </p>
                            
                            <?php if ($success_rate >= 80): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <strong>Great!</strong> Profile features are ready to use. You can now test all functionality.
                                </div>
                            <?php elseif ($success_rate >= 60): ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Almost there!</strong> Most features are ready, but some may have limited functionality.
                                </div>
                            <?php else: ?>
                                <div class="alert alert-danger">
                                    <i class="fas fa-times-circle me-2"></i>
                                    <strong>Issues detected!</strong> Several setup tasks failed. Please check the database configuration.
                                </div>
                            <?php endif; ?>
                            
                            <div class="d-flex justify-content-center gap-3 mt-4">
                                <a href="test-profile-features.php" class="btn btn-primary">
                                    <i class="fas fa-check-circle me-2"></i>Test All Features
                                </a>
                                <a href="profile.php" class="btn btn-outline-primary">
                                    <i class="fas fa-user me-2"></i>Go to Profile
                                </a>
                                <button class="btn btn-outline-secondary" onclick="location.reload()">
                                    <i class="fas fa-sync-alt me-2"></i>Run Setup Again
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

.progress {
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
}
</style>

<?php include 'includes/footer.php'; ?>
