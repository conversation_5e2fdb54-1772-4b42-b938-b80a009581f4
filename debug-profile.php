<?php
require_once 'config/config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "User not logged in. Please login first.";
    exit;
}

echo "<h2>Profile Debug Information</h2>";

try {
    $pdo = getDBConnection();
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // Get user data
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "<p style='color: green;'>✅ User data found</p>";
        echo "<h3>User Data:</h3>";
        echo "<pre>";
        foreach ($user as $key => $value) {
            if ($key !== 'password') { // Don't show password
                echo htmlspecialchars($key) . ": " . htmlspecialchars($value ?? 'NULL') . "\n";
            }
        }
        echo "</pre>";
        
        // Check specific fields that might cause issues
        echo "<h3>Field Analysis:</h3>";
        echo "<ul>";
        echo "<li>first_name: " . (isset($user['first_name']) ? "✅ Set" : "❌ Missing") . "</li>";
        echo "<li>last_name: " . (isset($user['last_name']) ? "✅ Set" : "❌ Missing") . "</li>";
        echo "<li>email: " . (isset($user['email']) ? "✅ Set" : "❌ Missing") . "</li>";
        echo "<li>phone: " . (isset($user['phone']) ? "✅ Set" : "❌ Missing") . "</li>";
        echo "<li>date_of_birth: " . (isset($user['date_of_birth']) ? "✅ Set" : "❌ Missing") . "</li>";
        echo "<li>gender: " . (isset($user['gender']) ? "✅ Set" : "❌ Missing") . "</li>";
        echo "</ul>";
        
    } else {
        echo "<p style='color: red;'>❌ User data not found</p>";
    }
    
    // Check users table structure
    echo "<h3>Users Table Structure:</h3>";
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p>Line: " . htmlspecialchars($e->getLine()) . "</p>";
}

echo "<br><br>";
echo "<a href='profile.php'>← Back to Profile</a>";
?>
