<?php
require_once 'config/config.php';

echo "<h2>Test Real Website Order Process</h2>";
echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: #f5f5f5;'>";

try {
    $pdo = getDBConnection();
    
    // Check if we have products
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
    $product_count = $stmt->fetch()['count'];
    
    echo "<h3>System Status Check</h3>";
    echo "<p><strong>Active Products:</strong> $product_count</p>";
    
    if ($product_count == 0) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4 style='color: #721c24;'>⚠️ No Products Found</h4>";
        echo "<p>You need to add some products first before testing the order process.</p>";
        echo "<p><a href='admin/products.php' style='color: #0d6efd;'>Go to Admin → Add Products</a></p>";
        echo "</div>";
    } else {
        // Get a sample product
        $stmt = $pdo->query("SELECT * FROM products WHERE status = 'active' LIMIT 1");
        $sample_product = $stmt->fetch();
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4 style='color: #155724;'>✅ Products Available</h4>";
        echo "<p><strong>Sample Product:</strong> " . htmlspecialchars($sample_product['name']) . "</p>";
        echo "<p><strong>Price:</strong> " . formatPrice($sample_product['price']) . "</p>";
        echo "</div>";
    }
    
    // Check cart functionality
    $session_id = session_id();
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM cart WHERE session_id = ? AND user_id IS NULL");
    $stmt->execute([$session_id]);
    $cart_count = $stmt->fetch()['count'];
    
    echo "<p><strong>Current Cart Items:</strong> $cart_count</p>";
    
    // Check orders table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders");
    $order_count = $stmt->fetch()['count'];
    echo "<p><strong>Total Orders in System:</strong> $order_count</p>";
    
    // Check latest order number
    $stmt = $pdo->query("SELECT order_number FROM orders ORDER BY created_at DESC LIMIT 1");
    $latest_order = $stmt->fetch();
    if ($latest_order) {
        echo "<p><strong>Latest Order Number:</strong> " . $latest_order['order_number'] . "</p>";
    }
    
    echo "<h3>Test Order Process</h3>";
    echo "<div style='margin: 20px 0;'>";
    
    if ($product_count > 0) {
        echo "<div class='test-steps'>";
        echo "<h4>Follow these steps to test the complete order process:</h4>";
        echo "<ol>";
        echo "<li><a href='products.php' target='_blank' style='color: #0d6efd;'>Go to Products Page</a> - Browse and add products to cart</li>";
        echo "<li><a href='cart.php' target='_blank' style='color: #0d6efd;'>Go to Cart Page</a> - Review cart items and proceed to checkout</li>";
        echo "<li><a href='checkout.php' target='_blank' style='color: #0d6efd;'>Go to Checkout Page</a> - Fill in customer information</li>";
        echo "<li>Review order and click <strong>'Confirm & Place Order'</strong></li>";
        echo "<li>See order confirmation with order number like <strong>TWN-2025-XXXX</strong></li>";
        echo "</ol>";
        echo "</div>";
        
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h5>Quick Test Links:</h5>";
        echo "<a href='index.php' class='btn' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;'>Home Page</a>";
        echo "<a href='products.php' class='btn' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;'>Products</a>";
        echo "<a href='cart.php' class='btn' style='background: #ffc107; color: black; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;'>Cart</a>";
        echo "<a href='checkout.php' class='btn' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;'>Checkout</a>";
        echo "</div>";
        
        // Test add to cart functionality
        if (isset($_POST['test_add_to_cart'])) {
            $product_id = (int)$_POST['product_id'];
            $quantity = 1;
            
            try {
                // Add to cart
                $stmt = $pdo->prepare("
                    INSERT INTO cart (session_id, product_id, quantity, created_at) 
                    VALUES (?, ?, ?, NOW())
                    ON DUPLICATE KEY UPDATE quantity = quantity + VALUES(quantity)
                ");
                $stmt->execute([$session_id, $product_id, $quantity]);
                
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                echo "<h4 style='color: #155724;'>✅ Product Added to Cart Successfully!</h4>";
                echo "<p>Product ID $product_id has been added to your cart.</p>";
                echo "<p><a href='cart.php' style='color: #0d6efd;'>View Cart</a></p>";
                echo "</div>";
            } catch (Exception $e) {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                echo "<h4 style='color: #721c24;'>❌ Error Adding to Cart</h4>";
                echo "<p>" . $e->getMessage() . "</p>";
                echo "</div>";
            }
        }
        
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h5>Quick Add to Cart Test:</h5>";
        echo "<form method='POST'>";
        echo "<input type='hidden' name='test_add_to_cart' value='1'>";
        echo "<input type='hidden' name='product_id' value='" . $sample_product['id'] . "'>";
        echo "<p>Test adding <strong>" . htmlspecialchars($sample_product['name']) . "</strong> to cart:</p>";
        echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>";
        echo "Add to Cart (Test)";
        echo "</button>";
        echo "</form>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4 style='color: #721c24;'>❌ Database Error</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h3>Expected Order Flow:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<ol>";
echo "<li><strong>Product Selection:</strong> User browses products and clicks 'Add to Cart'</li>";
echo "<li><strong>Cart Management:</strong> User reviews cart items and clicks 'Proceed to Checkout'</li>";
echo "<li><strong>Checkout Form:</strong> User fills in customer information and clicks 'Place Order'</li>";
echo "<li><strong>Order Review:</strong> User reviews order details and clicks 'Confirm & Place Order'</li>";
echo "<li><strong>Order Processing:</strong> System generates order number (TWN-2025-XXXX) and creates order</li>";
echo "<li><strong>Order Confirmation:</strong> User sees success page with order number and details</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🎯 Key Features Implemented:</h4>";
echo "<ul>";
echo "<li>✅ Sequential Order Numbers (TWN-2025-XXXX format)</li>";
echo "<li>✅ Add to Cart functionality on all product pages</li>";
echo "<li>✅ Cart management with quantity updates</li>";
echo "<li>✅ Complete checkout form with validation</li>";
echo "<li>✅ Order review page with 'Confirm & Place Order' button</li>";
echo "<li>✅ Order success page with confirmation details</li>";
echo "<li>✅ Order confirmation page accessible by order number</li>";
echo "<li>✅ Database integration with proper order storage</li>";
echo "</ul>";
echo "</div>";

echo "<p style='margin-top: 30px;'><a href='complete-order-workflow.php' style='color: #0d6efd;'>← Back to Test Workflow</a></p>";
echo "</div>";
?>
