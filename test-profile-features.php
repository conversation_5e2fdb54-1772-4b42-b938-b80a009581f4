<?php
require_once 'config/config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $_SESSION['intended_url'] = $_SERVER['REQUEST_URI'];
    redirect(SITE_URL . '/login.php');
}

$page_title = 'Profile Features Test';
$page_description = 'Test all profile-related features and functionality.';

$test_results = [];
$user_id = $_SESSION['user_id'];

// Test 1: Check if all profile pages exist
$profile_pages = [
    'profile.php' => 'My Profile',
    'my-orders.php' => 'My Orders',
    'wishlist.php' => 'Wishlist',
    'addresses.php' => 'Addresses',
    'order-detail.php' => 'Order Detail'
];

foreach ($profile_pages as $page => $title) {
    $test_results['pages'][$page] = [
        'title' => $title,
        'exists' => file_exists($page),
        'status' => file_exists($page) ? 'PASS' : 'FAIL'
    ];
}

// Test 2: Check database tables
try {
    $pdo = getDBConnection();
    
    // Check users table
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    $test_results['database']['users'] = [
        'title' => 'Users Table',
        'exists' => $stmt->rowCount() > 0,
        'status' => $stmt->rowCount() > 0 ? 'PASS' : 'FAIL'
    ];
    
    // Check user_addresses table
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_addresses'");
    $test_results['database']['user_addresses'] = [
        'title' => 'User Addresses Table',
        'exists' => $stmt->rowCount() > 0,
        'status' => $stmt->rowCount() > 0 ? 'PASS' : 'FAIL'
    ];
    
    // Check orders table
    $stmt = $pdo->query("SHOW TABLES LIKE 'orders'");
    $test_results['database']['orders'] = [
        'title' => 'Orders Table',
        'exists' => $stmt->rowCount() > 0,
        'status' => $stmt->rowCount() > 0 ? 'PASS' : 'FAIL'
    ];
    
    // Check wishlist table
    $stmt = $pdo->query("SHOW TABLES LIKE 'wishlist'");
    $test_results['database']['wishlist'] = [
        'title' => 'Wishlist Table',
        'exists' => $stmt->rowCount() > 0,
        'status' => $stmt->rowCount() > 0 ? 'PASS' : 'FAIL'
    ];
    
} catch (Exception $e) {
    $test_results['database']['error'] = [
        'title' => 'Database Connection',
        'exists' => false,
        'status' => 'FAIL',
        'error' => $e->getMessage()
    ];
}

// Test 3: Check user data
try {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch();
    
    $test_results['user_data']['profile'] = [
        'title' => 'User Profile Data',
        'exists' => $user !== false,
        'status' => $user !== false ? 'PASS' : 'FAIL',
        'data' => $user ? [
            'name' => $user['first_name'] . ' ' . $user['last_name'],
            'email' => $user['email'],
            'phone' => $user['phone'] ?? 'Not set'
        ] : null
    ];
    
} catch (Exception $e) {
    $test_results['user_data']['profile'] = [
        'title' => 'User Profile Data',
        'exists' => false,
        'status' => 'FAIL',
        'error' => $e->getMessage()
    ];
}

// Test 4: Check user addresses
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM user_addresses WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $address_count = $stmt->fetch()['count'];
    
    $test_results['user_data']['addresses'] = [
        'title' => 'User Addresses',
        'exists' => true,
        'status' => 'PASS',
        'count' => $address_count
    ];
    
} catch (Exception $e) {
    $test_results['user_data']['addresses'] = [
        'title' => 'User Addresses',
        'exists' => false,
        'status' => 'FAIL',
        'error' => $e->getMessage()
    ];
}

// Test 5: Check user orders
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM orders WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $order_count = $stmt->fetch()['count'];
    
    $test_results['user_data']['orders'] = [
        'title' => 'User Orders',
        'exists' => true,
        'status' => 'PASS',
        'count' => $order_count
    ];
    
} catch (Exception $e) {
    $test_results['user_data']['orders'] = [
        'title' => 'User Orders',
        'exists' => false,
        'status' => 'FAIL',
        'error' => $e->getMessage()
    ];
}

// Test 6: Check user wishlist
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM wishlist WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $wishlist_count = $stmt->fetch()['count'];
    
    $test_results['user_data']['wishlist'] = [
        'title' => 'User Wishlist',
        'exists' => true,
        'status' => 'PASS',
        'count' => $wishlist_count
    ];
    
} catch (Exception $e) {
    $test_results['user_data']['wishlist'] = [
        'title' => 'User Wishlist',
        'exists' => false,
        'status' => 'FAIL',
        'error' => $e->getMessage()
    ];
}

// Test 7: Check AJAX endpoints
$ajax_endpoints = [
    'ajax/cancel-order.php' => 'Cancel Order',
    'ajax/check-order-status.php' => 'Check Order Status',
    'ajax/add_to_wishlist.php' => 'Add to Wishlist'
];

foreach ($ajax_endpoints as $endpoint => $title) {
    $test_results['ajax'][$endpoint] = [
        'title' => $title,
        'exists' => file_exists($endpoint),
        'status' => file_exists($endpoint) ? 'PASS' : 'FAIL'
    ];
}

include 'includes/header.php';
?>

<div class="container-lg my-5">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h2 mb-1 text-primary">
                        <i class="fas fa-check-circle me-2"></i>Profile Features Test
                    </h1>
                    <p class="text-muted">Comprehensive testing of all profile-related features</p>
                </div>
                <div>
                    <button class="btn btn-outline-primary" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh Tests
                    </button>
                </div>
            </div>

            <!-- Test Results -->
            <?php foreach ($test_results as $category => $tests): ?>
                <div class="card mb-4 border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-<?php echo $category === 'pages' ? 'file' : ($category === 'database' ? 'database' : ($category === 'ajax' ? 'code' : 'user')); ?> me-2"></i>
                            <?php echo ucwords(str_replace('_', ' ', $category)); ?> Tests
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($tests as $test_key => $test): ?>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card h-100 border-<?php echo $test['status'] === 'PASS' ? 'success' : 'danger'; ?>">
                                        <div class="card-body text-center">
                                            <div class="mb-2">
                                                <i class="fas fa-<?php echo $test['status'] === 'PASS' ? 'check-circle text-success' : 'times-circle text-danger'; ?> fa-2x"></i>
                                            </div>
                                            <h6 class="card-title"><?php echo $test['title']; ?></h6>
                                            <span class="badge bg-<?php echo $test['status'] === 'PASS' ? 'success' : 'danger'; ?> mb-2">
                                                <?php echo $test['status']; ?>
                                            </span>
                                            
                                            <?php if (isset($test['count'])): ?>
                                                <p class="small text-muted mb-0">Count: <?php echo $test['count']; ?></p>
                                            <?php endif; ?>
                                            
                                            <?php if (isset($test['data'])): ?>
                                                <div class="small text-start mt-2">
                                                    <?php foreach ($test['data'] as $key => $value): ?>
                                                        <div><strong><?php echo ucfirst($key); ?>:</strong> <?php echo htmlspecialchars($value); ?></div>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if (isset($test['error'])): ?>
                                                <div class="small text-danger mt-2">
                                                    Error: <?php echo htmlspecialchars($test['error']); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>

            <!-- Quick Links to Profile Features -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-link me-2"></i>Quick Access to Profile Features
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-6 mb-3">
                            <a href="profile.php" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="fas fa-user fa-2x mb-2"></i>
                                <span>My Profile</span>
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <a href="my-orders.php" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="fas fa-shopping-bag fa-2x mb-2"></i>
                                <span>My Orders</span>
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <a href="wishlist.php" class="btn btn-outline-danger w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="fas fa-heart fa-2x mb-2"></i>
                                <span>Wishlist</span>
                            </a>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <a href="addresses.php" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="fas fa-map-marker-alt fa-2x mb-2"></i>
                                <span>Addresses</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Summary -->
            <div class="mt-4 text-center">
                <?php
                $total_tests = 0;
                $passed_tests = 0;
                
                foreach ($test_results as $category => $tests) {
                    foreach ($tests as $test) {
                        $total_tests++;
                        if ($test['status'] === 'PASS') {
                            $passed_tests++;
                        }
                    }
                }
                
                $success_rate = $total_tests > 0 ? round(($passed_tests / $total_tests) * 100, 1) : 0;
                ?>
                
                <div class="alert alert-<?php echo $success_rate >= 90 ? 'success' : ($success_rate >= 70 ? 'warning' : 'danger'); ?> d-inline-block">
                    <h5 class="mb-2">
                        <i class="fas fa-chart-pie me-2"></i>Test Summary
                    </h5>
                    <p class="mb-0">
                        <strong><?php echo $passed_tests; ?></strong> out of <strong><?php echo $total_tests; ?></strong> tests passed
                        (<strong><?php echo $success_rate; ?>%</strong> success rate)
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}
</style>

<?php include 'includes/footer.php'; ?>
