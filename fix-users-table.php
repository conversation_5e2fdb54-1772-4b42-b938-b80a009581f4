<?php
require_once 'config/config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "Please login first.";
    exit;
}

$results = [];

try {
    $pdo = getDBConnection();
    
    // Check current table structure
    $stmt = $pdo->query("DESCRIBE users");
    $existing_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $results[] = "✅ Current columns in users table: " . implode(', ', $existing_columns);
    
    // Required columns for profile functionality
    $required_columns = [
        'phone' => 'VARCHAR(20) NULL',
        'date_of_birth' => 'DATE NULL',
        'gender' => 'ENUM("male", "female", "other") NULL',
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];
    
    $columns_added = 0;
    
    foreach ($required_columns as $column => $definition) {
        if (!in_array($column, $existing_columns)) {
            try {
                $sql = "ALTER TABLE users ADD COLUMN {$column} {$definition}";
                $pdo->exec($sql);
                $results[] = "✅ Added column: {$column}";
                $columns_added++;
            } catch (Exception $e) {
                $results[] = "❌ Failed to add column {$column}: " . $e->getMessage();
            }
        } else {
            $results[] = "ℹ️ Column {$column} already exists";
        }
    }
    
    if ($columns_added > 0) {
        $results[] = "🎉 Successfully added {$columns_added} columns to users table";
    } else {
        $results[] = "✅ All required columns already exist";
    }
    
    // Test user data retrieval
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    
    if ($user) {
        $results[] = "✅ User data retrieved successfully";
        $results[] = "User ID: " . $user['id'];
        $results[] = "Name: " . ($user['first_name'] ?? 'N/A') . " " . ($user['last_name'] ?? 'N/A');
        $results[] = "Email: " . ($user['email'] ?? 'N/A');
        $results[] = "Phone: " . ($user['phone'] ?? 'Not set');
        $results[] = "Date of Birth: " . ($user['date_of_birth'] ?? 'Not set');
        $results[] = "Gender: " . ($user['gender'] ?? 'Not set');
    } else {
        $results[] = "❌ Could not retrieve user data";
    }
    
} catch (Exception $e) {
    $results[] = "❌ Database error: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Fix Users Table</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <h1>Users Table Fix Results</h1>
    
    <?php foreach ($results as $result): ?>
        <div class="result <?php 
            if (strpos($result, '✅') !== false || strpos($result, '🎉') !== false) echo 'success';
            elseif (strpos($result, '❌') !== false) echo 'error';
            elseif (strpos($result, 'ℹ️') !== false) echo 'info';
            else echo 'info';
        ?>">
            <?php echo htmlspecialchars($result); ?>
        </div>
    <?php endforeach; ?>
    
    <div style="margin-top: 20px;">
        <a href="profile.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            Test Profile Page
        </a>
        <a href="debug-profile.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
            Debug Profile
        </a>
    </div>
</body>
</html>
